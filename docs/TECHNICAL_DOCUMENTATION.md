# Technical Documentation: Cosplay Downloader GUI

This document outlines the core features and workflows of the Cosplay Downloader application from a technical perspective, focusing on the sequence of operations rather than specific code implementation details.

## 1. Application Startup and Initialization

When the application is launched, it performs a series of initialization steps in the following order:

1.  **UI Setup**: The main window and all its components (input fields, buttons, log area) are created and displayed.
2.  **Backend Services**: Instances of the URL history manager and the image hash manager are created, connecting the application to its underlying SQLite databases.
3.  **Name Extraction**: The application immediately scans the designated image directory. It reads the filenames of all existing images and parses them to extract unique cosplayer and character names. These names are used to populate the autocompletion suggestions in the input fields.
4.  **Background Hash Scanning**: Simultaneously, a background process kicks off to ensure the visual duplicate database is up-to-date. This process:
    a.  Removes any database entries for images that have been deleted from the filesystem.
    b.  Scans the image directory for any new or modified images that are not yet in the hash database.
    c.  For each new/modified image, it calculates a perceptual hash and saves it to the database.

This entire startup process ensures that by the time the user can interact with the application, it is already prepared with the latest data for autocompletion and duplicate detection.

## 2. The Image Download Workflow

The core functionality of the application is the image download process. This is the sequence of events that occurs when the user clicks the "Herunterladen" (Download) button:

1.  **Input Gathering**: The application collects all the provided inputs: the list of cosplayer names, character names, and image URLs.

2.  **URL Pre-Check**: Before initiating any downloads, the application checks the list of URLs against its internal history database. 
    -   If any URLs have been downloaded in the past, a dialog box appears, warning the user. 
    -   The user is given the choice to download only the new URLs, download all of them again, or cancel the operation entirely.

3.  **Download Process Begins**: A background process is started to handle the downloads, ensuring the UI remains responsive. For each URL in the list, the following steps are taken:

4.  **Visual Duplicate Check (if enabled)**:
    a.  The image from the URL is temporarily downloaded.
    b.  A perceptual hash of this image is calculated.
    c.  This hash is compared against all hashes in the existing database.
    d.  **If a visually similar image is found**, the download process for this specific URL is paused. A dialog box appears, showing the new image side-by-side with the existing one(s). The user can then decide to:
        -   **Skip**: Discard this download and move to the next URL.
        -   **Overwrite**: Delete the old image(s) and replace it with the new one.
        -   **Download Anyway**: Keep the old image(s) and save the new one as a separate file.
    e.  The download process resumes based on the user's choice.

5.  **File Naming and Saving**:
    a.  A filename is generated based on the provided cosplayer and character names.
    b.  The system checks if a file with this name already exists. If so, it appends a number (e.g., `02`, `03`) to create a unique filename.
    c.  The image is downloaded and saved to the designated directory.

6.  **Image Compression (if enabled)**:
    a.  After saving, the application checks if the image should be compressed. This is typically done for large images that do not come from sources that provide pre-compressed images (like Twitter).
    b.  If needed, the image is compressed to a smaller file size using a high-quality JPEG compression algorithm.

7.  **Finalization**: 
    a.  The application verifies that the downloaded file is a valid, non-corrupt image.
    b.  The URL of the successfully downloaded image is added to the history database.
    c.  The perceptual hash of the new image is added to the hash database, making it available for future duplicate checks.

8.  **Completion**: Once all URLs have been processed, a final summary message is displayed in the log, and the application is ready for the next operation.

## 3. Custom User Interface Features

To enhance usability, the application employs several custom UI components:

-   **Tag-Based Input**: Instead of simple text fields, the inputs for cosplayer and character names are tag fields. This allows for the clear entry of multiple names and provides an easy way to remove individual entries.
-   **Flow Layout**: This custom layout allows the tags to wrap neatly within the input area, accommodating a variable number of entries without breaking the UI structure.
-   **Interactive Logging**: The log panel provides real-time, color-coded feedback on the application's status, with clear icons for success, warnings, and errors.
-   **Clickable Image Previews**: In the duplicate detection dialog, the image previews are clickable, allowing the user to quickly open the files in their default image viewer for closer inspection.
